import {
   AfterViewInit,
   booleanAttribute,
   Component,
   inject,
   Input,
   OnChanges,
   OnInit,
   SimpleChanges,
   ViewChild
} from '@angular/core';
import {MatTableDataSource} from '@angular/material/table';
import {MatPaginator} from '@angular/material/paginator';
import {animate, state, style, transition, trigger} from '@angular/animations';
import {CustomAction, DataTableService, DataType} from './data-table.service';
import {SelectionModel} from '@angular/cdk/collections';
import {equalIdentifiables} from '../../utility/utility';
import {inOut} from '../../animations/in-out';
import {FormControl} from '@angular/forms';

@Component({
   selector: 'app-data-table',
   templateUrl: './data-table.component.html',
   styles: [`
      tr.form-row {
         height: 0;
      }

      .sticky-row {
         position: sticky;
         z-index: 1;
      }

      .r-1 {
         top: 0;
      }

      .r-2 {
         top: 56px;
         height: 2.5em;
      }
   `],
   animations: [
      trigger('detailExpand', [
         state('collapsed,void', style({height: '0px', minHeight: '0'})),
         state('expanded', style({height: '*'})),
         transition('expanded <=> collapsed',
            animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
      ]),
      inOut
   ],
   standalone: false
})
export class DataTableComponent implements OnInit, AfterViewInit, OnChanges {
   @Input({required: true}) properties: (string | ((obj: any) => any))[] = [];
   @Input({required: true}) headers: string[] = [];
   @Input({required: true}) data: any[] = [];
   @Input({required: true}) formClass: any;
   @Input({required: true}) type!: DataType;
   @Input({transform: booleanAttribute}) noPaginator = false;
   @Input({transform: booleanAttribute}) search = false;
   @Input() filterPredicate?: (data: any, filter: string) => boolean;
   @Input() customActions?: CustomAction[];
   @Input({transform: booleanAttribute}) allowDelete = true;

   @ViewChild(MatPaginator) paginator!: MatPaginator;

   dataSource = new MatTableDataSource<any>();
   selection = new SelectionModel<any>(true, [], false, equalIdentifiables);

   expanded: any = null;

   sResult = inject(DataTableService);

   filter?: FormControl;

   ngOnInit() {
      if (this.search) {
         this.filter = new FormControl('');
         this.filter.valueChanges.subscribe(f => this.dataSource.filter = f ?? '');
      }

      this.dataSource.data = this.data;

      if (this.filterPredicate) {
         this.dataSource.filterPredicate = this.filterPredicate;
      }
   }

   ngAfterViewInit() {
      if (!this.noPaginator) {
         this.dataSource.paginator = this.paginator;
      }
   }

   ngOnChanges(changes: SimpleChanges): void {
      if (changes.data) {
         setTimeout(() => this.dataSource.data = changes.data.currentValue, 225);
      }
   }

   get headerColumns(): string[] {
      return ['dataTableEmpty', ...this.headers, 'dataTableExpand'];
   }

   get tableColumns(): string[] {
      return ['dataTableSelect', ...this.headers, 'dataTableExpand'];
   }

   get firstHeaderRow(): string[] {
      return ['dataTableSelect', 'dataTableActions'];
   }

   expand(row: any): void {
      this.expanded = row;
      this.sResult.resetResult();
   }

   shrink(submit: boolean) {
      // TODO(vlado): Optimize result to be a function that returns the result
      //              instead of settings it on every change in the inner form
      const result = this.sResult.result;
      if (submit && result) {
         this.sResult.submit(result, this.type);
      }
      this.expanded = null;
   }

   deleteSelected(): void {
      this.sResult.delete(this.selection.selected, this.type);
   }

   clearSelection(): void {
      this.selection.clear();
   }

   isAllSelected(): boolean {
      return this.selection.selected.length === this.dataSource.data.length;
   }

   masterToggle(): void {
      if (this.isAllSelected()) {
         this.selection.clear();
      } else {
         this.selection.select(...this.dataSource.data);
      }
   }
}
