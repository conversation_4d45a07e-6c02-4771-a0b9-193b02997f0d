import {inject, Injectable} from '@angular/core';
import {NotificationService} from './notification.service';

@Injectable({
   providedIn: 'root'
})
export class VersioningService {
   currentVersion = '2.20.1';

   private sNotification = inject(NotificationService);

   init(): void {
      this.queryUpdates(window).then();
      setInterval(() => this.queryUpdates(window), 60_000);
   }

   private async queryUpdates(wdw: Window): Promise<void> {
      const response = await fetch('/assets/version');
      if (response.ok) {
         const text = await response.text();
         if (text.startsWith('{')) {
            const {version} = JSON.parse(text);
            if (this.currentVersion !== version) {
               this.sNotification.displayReloadNotification(10)
                  .subscribe(() => wdw.location.reload());
            }
         } else {
            if (this.currentVersion !== text.trim()) {
               this.sNotification.displayReloadNotification(10)
                  .subscribe(() => wdw.location.reload());
            }
         }
      }
   }
}
