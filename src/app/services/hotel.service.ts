import {inject, Injectable} from '@angular/core';
import {AccommodationPlace, Bank, Hotel} from 'src/app/data/hotel';
import {Identifiable, MaybeID} from '../data/identifiable';
import {Currency, LocalTime} from '../data/common';
import {addId, serverUrl} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';
import {map, Observable, of, tap} from 'rxjs';
import {parseDate} from '../utility/utility';

interface HotelDTO extends Identifiable {
   place: string;
   accommodationPlaceUin: string;

   bankName: string;
   iban: string;
   bic: string;

   checkInTime: string;
   checkOutTime: string;
   zoneId: string;
   expiryDate?: string;
   touristTax?: number;
   defaultCurrency: Currency;
}

interface HotelDTORemote
   extends Omit<HotelDTO, 'checkInTime' | 'checkOutTime' | 'expiryDate'> {
   checkInTime: number[];
   checkOutTime: number[];
}

const toLocalTime = (time: string): LocalTime => {
   const [hour, minute, second] = time.split(':').map(t => parseInt(t, 10));
   return {hour, minute, second};
};

const toTimeArray = (t: LocalTime): number[] => [t.hour, t.minute, t.second];

@Injectable({
   providedIn: 'root'
})
export class HotelService {
   private url = serverUrl('hotel');
   private idUrl = addId.bind(null, this.url);

   private http = inject(HttpClient);

   private hotel?: Hotel;

   getCurrent(): Observable<Hotel> {
      return this.hotel ? of(this.hotel) : this.getCurrentNoCache();
   }

   getCurrentNoCache(): Observable<Hotel> {
      return this.http.get<HotelDTO>(`${this.url}/current`).pipe(
         map(dto => this.mapToLocal(dto)),
         tap(hotel => this.hotel = hotel),
      );
   }

   update(newItem: Hotel): Observable<void> {
      return this.http.put<void>(this.idUrl(newItem.id), this.mapToRemote(newItem))
         .pipe(tap(this.hotel = undefined));
   }

   private mapToLocal(dto: HotelDTO): Hotel {
      const {
         bankName,
         iban,
         bic,
         place,
         accommodationPlaceUin,
         checkInTime,
         checkOutTime,
         expiryDate,
         ...rest
      } = dto;

      const bank: Bank = {bankName, iban, bic};
      const accommodationPlace: AccommodationPlace = {place, accommodationPlaceUin};

      return {
         bank,
         accommodationPlace,
         checkInTime: toLocalTime(checkInTime),
         checkOutTime: toLocalTime(checkOutTime),
         expiryDate: parseDate(expiryDate),
         ...rest,
      };
   }

   private mapToRemote(hotel: MaybeID<Hotel>): MaybeID<HotelDTORemote> {
      const {bank, accommodationPlace, checkInTime, checkOutTime, ...rest} = hotel;

      return {
         ...rest,
         ...bank,
         ...accommodationPlace,
         checkInTime: toTimeArray(checkInTime),
         checkOutTime: toTimeArray(checkOutTime),
      };
   }
}
