import {Component, inject, Input, OnInit} from '@angular/core';
import {UntypedFormBuilder, Validators} from '@angular/forms';
import {ReservationSource, ReservationSourceCode, ReservationSourceLables} from '../../data/reservation-source';
import {DataTableService} from '../../settings/data-table/data-table.service';

@Component({
  selector: 'app-reservation-source-form',
  templateUrl: './reservation-source-form.component.html',
  standalone: false
})
export class ReservationSourceFormComponent implements OnInit {
  @Input() data?: ReservationSource;
  @Input() edit = false;

  form = inject(UntypedFormBuilder).group({
    id: '',
    name: ['', Validators.required],
    code: '',
    channelCode: null,
    requireIdentifier: [false]
  });

  sourceCodes = Object.values(ReservationSourceCode).map(value => ({
    value,
    label: ReservationSourceLables[value]
  }));

  private sDataTable = inject(DataTableService);

  get valid(): boolean {
    return this.form.valid;
  }

  get value(): ReservationSource {
    return this.form.value;
  }

  ngOnInit(): void {
    if (this.data) {
      this.form.patchValue(this.data);
    }

    // Only update DataTableService result when used in data table context
    if (this.edit) {
      this.form.valueChanges.subscribe(v => this.sDataTable.result = v);
    }
  }
}
