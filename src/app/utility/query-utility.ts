import {
   Query,
   QueryDataPrimitive,
   QueryResult,
   QueryType,
   SingleQueryResult
} from '../data/queries/query';
import {DateTime} from 'luxon';
import {Workbook} from 'exceljs';
import {saveAs} from 'file-saver';
import {dateFmt} from './utility';
import {DateTimePipe} from '../pipes/date-time.pipe';

const csvFileName = (query: Query) => `${query.name}.csv`;

const estiFileName = (query: Query, date: DateTime) =>
   `${query.name} за ${dateFmt(date)}.csv`;

const xlsxFileName = (query: Query) => `${query.name}.xlsx`;

const csvString = (content: string) =>
   encodeURI(`data:text/csv;charset=utf-8,${content}`);

const singleResultToSheets = (queryName: string,
                              result: SingleQueryResult): [string, QueryDataPrimitive[][]][] => {
   switch (result.type) {
      case QueryType.table:
         if (result.columnNames.length) {
            return [[queryName, [result.columnNames, ...result.data]]];
         } else {
            return [[queryName, result.data]];
         }
      case QueryType.periodicTable:
         if (result.columnNames.length) {
            return result.data.map(r =>
               [DateTimePipe.toString(r.time), [result.columnNames, ...r.data]]);
         } else {
            return result.data.map(r => [DateTimePipe.toString(r.time), r.data]);
         }
      case QueryType.groupedTable:
         return [[queryName, result.data.flatMap(r => [[r.header], ...r.data])]];
   }
};

const resultToSheets = (query: Query,
                        result: QueryResult): [string, QueryDataPrimitive[][]][] => {
   switch (result.type) {
      case QueryType.table:
      case QueryType.periodicTable:
      case QueryType.groupedTable:
         return singleResultToSheets(query.name, result);
      case QueryType.multiple: {
         const r = Object.entries(result.results).flatMap(([name, queryResult]) =>
            singleResultToSheets(name, queryResult));
         return [[query.name, r.flatMap(([sheet, rows]) => [[sheet], ...rows])]];
      }
   }
};

// Format currencies properly in Excel, right now it's text and it's pretty useless
export const downloadXlsx = (query: Query,
                             result: QueryResult) => {
   const workbook = new Workbook();
   const sheets = resultToSheets(query, result);

   for (const [name, values] of sheets) {
      const sheet = workbook.addWorksheet(name);
      values.forEach(row => sheet.addRow(row));
   }

   workbook.xlsx.writeBuffer().then(data => {
      const blob = new Blob([data],
         {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
      const fileName = xlsxFileName(query);
      saveAs(blob, fileName);
   });
};

export const downloadEstiCsv = (query: Query,
                                result: QueryDataPrimitive[][],
                                date: DateTime) => {
   const fileName = estiFileName(query, date);
   const data = csvString(result.map(row => row.join(';')).join('\n'));
   saveAs(data, fileName);
};

export const downloadCsv = (query: Query, result: QueryDataPrimitive[][]) => {
   const fileName = csvFileName(query);
   const data = csvString(result.map(row => row.join('\t')).join('\n'));
   saveAs(data, fileName);
};

export const isESTI = (query?: Query) => !!query?.name.toLowerCase().includes('ести');
