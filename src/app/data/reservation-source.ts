import {ID, Identifiable} from './identifiable';

export enum ReservationSourceCode {
   booking = 'BDC',
   airbnb = 'ABB',
   expedia = 'EXP'
}

export const ReservationSourceLables: Record<ReservationSourceCode, string> = {
   [ReservationSourceCode.booking]: 'Booking.com',
   [ReservationSourceCode.airbnb]: 'AirBnb',
   [ReservationSourceCode.expedia]: 'Expedia'
};

export interface ReservationSource extends Identifiable {
   name: string;
   code?: string;
   isDefault?: boolean;
   financialAccount?: ID;
   channelCode?: ReservationSourceCode;
   requireIdentifier: boolean;
}
