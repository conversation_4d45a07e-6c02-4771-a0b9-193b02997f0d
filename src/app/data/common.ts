import {DateTime} from 'luxon';

export enum Currency {
   bgn = 'BGN',
   eur = 'EUR',
   usd = 'USD',
}

export interface Money {
   amount: number;
   currency: Currency;
}

export enum DayOfWeek {
   monday = 'Понеделник',
   tuesday = 'Вторник',
   wednesday = 'Сряда',
   thursday = 'Четрърък',
   friday = 'Петък',
   saturday = 'Събота',
   sunday = 'Неделя',
}

export interface DateRange {
   start: DateTime;
   end: DateTime;
}

export type DateRange2 = DateRange;

export interface LocalTime {
   hour: number;
   minute: number;
   second: number;
}

export type AppLocalizedString = Record<'en' | 'bg', string>;
