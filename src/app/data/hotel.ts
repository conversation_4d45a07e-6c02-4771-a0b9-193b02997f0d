import {Currency, LocalTime} from './common';
import {Identifiable} from './identifiable';
import {DateTime} from 'luxon';

export interface Bank {
   bankName: string;
   iban: string;
   bic: string;
}

export interface AccommodationPlace {
   place: string;
   accommodationPlaceUin: string;
}

export interface Hotel extends Identifiable {
   bank: Bank;
   accommodationPlace: AccommodationPlace;
   checkInTime: LocalTime;
   checkOutTime: LocalTime;
   zoneId: string;
   expiryDate?: DateTime;
   touristTax?: number;
   defaultCurrency: Currency;
}
